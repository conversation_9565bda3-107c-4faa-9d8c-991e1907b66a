/**
 * Cascade Workflow - Orchestrates the gate system
 *
 * Runs user input through a series of gates to determine the appropriate
 * processing mode and provide clarification when needed.
 */

import { mutation, query } from "../_generated/server";
import { v } from "convex/values";
import {
  type CascadeResult,
  type DetailedGateResult,
  type GateContext,
  type GateConfig
} from "./sharedTypes";

// Import gate functions
import { vaguenessGateInternal } from "./gates/vaguenessGate";
import { focusGateInternal } from "./gates/focusGate";

/**
 * Gate configuration - defines the order and settings for each gate
 */
const GATE_CONFIGS: GateConfig[] = [
  {
    name: "vagueness",
    enabled: true,
    weight: 1.0,
    timeout: 5000,
    retries: 1
  },
  {
    name: "focus",
    enabled: true,
    weight: 1.0,
    timeout: 5000,
    retries: 1
  }
  // Additional gates can be added here
];

/**
 * Execute the cascade workflow for a given user input
 */
export const executeCascade = mutation({
  args: {
    conversationId: v.id("conversations"),
    userInput: v.string(),
    previousMessages: v.optional(v.array(v.object({
      role: v.union(v.literal("user"), v.literal("assistant"), v.literal("system")),
      content: v.string(),
      timestamp: v.number()
    })))
  },
  handler: async (ctx, { conversationId, userInput, previousMessages = [] }): Promise<CascadeResult> => {
    const startTime = Date.now();

    // Validate conversation exists
    const conversation = await ctx.db.get(conversationId);
    if (!conversation) {
      throw new Error("Conversation not found");
    }

    const gateResults: Record<string, DetailedGateResult> = {};
    let failedAt: string | undefined;

    // Create gate context
    const gateContext: GateContext = {
      userInput,
      conversationId,
      previousMessages: previousMessages.map(msg => ({
        role: msg.role,
        content: msg.content,
        createdAt: msg.timestamp
      })),
      priorGateResults: {}
    };

    // Execute each enabled gate in sequence
    for (const gateConfig of GATE_CONFIGS) {
      if (!gateConfig.enabled) {
        continue;
      }

      try {
        let gateResult: DetailedGateResult;

        // Execute the appropriate gate
        switch (gateConfig.name) {
          case "vagueness":
            gateResult = await vaguenessGateInternal(ctx, gateContext);
            break;

          case "focus":
            gateResult = await focusGateInternal(ctx, gateContext);
            break;

          default:
            throw new Error(`Unknown gate: ${gateConfig.name}`);
        }

        // Store the result
        gateResults[gateConfig.name] = gateResult;
        gateContext.priorGateResults[gateConfig.name] = gateResult;

        // If gate failed, stop the cascade
        if (gateResult.status === "fail") {
          failedAt = gateConfig.name;
          break;
        }

      } catch (error) {
        console.error(`Gate ${gateConfig.name} error:`, error);

        // Create error result
        gateResults[gateConfig.name] = {
          status: "fail",
          message: "Internal error occurred during gate processing.",
          confidence: 0,
          processingTime: 0,
          metadata: {
            error: "gate_execution_error"
          }
        };

        failedAt = gateConfig.name;
        break;
      }
    }

    const totalProcessingTime = Date.now() - startTime;

    // Determine overall result
    const overallStatus = failedAt ? "fail" : "pass";
    const overallMessage = failedAt
      ? gateResults[failedAt]?.message || "Processing failed"
      : "Input passed all gates and is ready for processing.";

    return {
      status: overallStatus,
      message: overallMessage,
      gateResults,
      totalProcessingTime,
      failedAt
    };
  }
});

/**
 * Get the current cascade status for a conversation
 */
export const getCascadeStatus = query({
  args: {
    conversationId: v.id("conversations")
  },
  handler: async (ctx, { conversationId }) => {
    // This would typically fetch stored cascade results from the database
    // For now, return a basic structure
    return {
      conversationId,
      gateResults: {},
      lastExecuted: null,
      status: "pending" as const
    };
  }
});

/**
 * Get available gate configurations
 */
export const getGateConfigs = query({
  args: {},
  handler: async () => {
    return GATE_CONFIGS;
  }
});
